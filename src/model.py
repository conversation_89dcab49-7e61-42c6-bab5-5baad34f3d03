import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, List
import logging
from pathlib import Path
import os

logger = logging.getLogger(__name__)

class BasicBlock3D(nn.Module):
    """3D ResNet基本块"""
    expansion = 1

    def __init__(self, inplanes, planes, stride=1, downsample=None):
        super(BasicBlock3D, self).__init__()
        self.conv1 = nn.Conv3d(inplanes, planes, kernel_size=3, stride=stride, padding=1, bias=False)
        self.bn1 = nn.BatchNorm3d(planes)
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv3d(planes, planes, kernel_size=3, stride=1, padding=1, bias=False)
        self.bn2 = nn.BatchNorm3d(planes)
        self.downsample = downsample
        self.stride = stride

    def forward(self, x):
        residual = x

        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        out = self.conv2(out)
        out = self.bn2(out)

        if self.downsample is not None:
            residual = self.downsample(x)

        out += residual
        out = self.relu(out)

        return out


class Bottleneck3D(nn.Module):
    """3D ResNet瓶颈块"""
    expansion = 4

    def __init__(self, inplanes, planes, stride=1, downsample=None):
        super(Bottleneck3D, self).__init__()
        self.conv1 = nn.Conv3d(inplanes, planes, kernel_size=1, bias=False)
        self.bn1 = nn.BatchNorm3d(planes)
        self.conv2 = nn.Conv3d(planes, planes, kernel_size=3, stride=stride, padding=1, bias=False)
        self.bn2 = nn.BatchNorm3d(planes)
        self.conv3 = nn.Conv3d(planes, planes * self.expansion, kernel_size=1, bias=False)
        self.bn3 = nn.BatchNorm3d(planes * self.expansion)
        self.relu = nn.ReLU(inplace=True)
        self.downsample = downsample
        self.stride = stride

    def forward(self, x):
        residual = x

        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        out = self.conv2(out)
        out = self.bn2(out)
        out = self.relu(out)

        out = self.conv3(out)
        out = self.bn3(out)

        if self.downsample is not None:
            residual = self.downsample(x)

        out += residual
        out = self.relu(out)

        return out


class ResNet3D(nn.Module):
    """3D ResNet网络"""

    def __init__(self, block, layers, num_classes=1000, in_channels=1):
        super(ResNet3D, self).__init__()
        self.inplanes = 64
        
        # 初始卷积层
        self.conv1 = nn.Conv3d(in_channels, 64, kernel_size=7, stride=2, padding=3, bias=False)
        self.bn1 = nn.BatchNorm3d(64)
        self.relu = nn.ReLU(inplace=True)
        self.maxpool = nn.MaxPool3d(kernel_size=3, stride=2, padding=1)
        
        # ResNet层
        self.layer1 = self._make_layer(block, 64, layers[0])
        self.layer2 = self._make_layer(block, 128, layers[1], stride=2)
        self.layer3 = self._make_layer(block, 256, layers[2], stride=2)
        self.layer4 = self._make_layer(block, 512, layers[3], stride=2)
        
        # 全局平均池化和分类器
        self.avgpool = nn.AdaptiveAvgPool3d((1, 1, 1))
        self.fc = nn.Linear(512 * block.expansion, num_classes)
        
        # 权重初始化
        self._initialize_weights()

    def _make_layer(self, block, planes, blocks, stride=1):
        downsample = None
        if stride != 1 or self.inplanes != planes * block.expansion:
            downsample = nn.Sequential(
                nn.Conv3d(self.inplanes, planes * block.expansion,
                         kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm3d(planes * block.expansion),
            )

        layers = []
        layers.append(block(self.inplanes, planes, stride, downsample))
        self.inplanes = planes * block.expansion
        for i in range(1, blocks):
            layers.append(block(self.inplanes, planes))

        return nn.Sequential(*layers)

    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv3d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.BatchNorm3d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.maxpool(x)

        x = self.layer1(x)
        x = self.layer2(x)
        x = self.layer3(x)
        x = self.layer4(x)

        x = self.avgpool(x)
        x = torch.flatten(x, 1)
        x = self.fc(x)

        return x


def resnet50_3d(num_classes=1000, pretrained=False, pretrained_path=None, **kwargs):
    """构建3D ResNet-50模型"""
    model = ResNet3D(Bottleneck3D, [3, 4, 6, 3], num_classes=num_classes, **kwargs)
    
    if pretrained and pretrained_path:
        model = load_pretrained_weights(model, pretrained_path)
    
    return model


def resnet34_3d(num_classes=1000, pretrained=False, pretrained_path=None, **kwargs):
    """构建3D ResNet-34模型"""
    model = ResNet3D(BasicBlock3D, [3, 4, 6, 3], num_classes=num_classes, **kwargs)
    
    if pretrained and pretrained_path:
        model = load_pretrained_weights(model, pretrained_path)
    
    return model


def resnet18_3d(num_classes=1000, pretrained=False, pretrained_path=None, **kwargs):
    """构建3D ResNet-18模型"""
    model = ResNet3D(BasicBlock3D, [2, 2, 2, 2], num_classes=num_classes, **kwargs)
    
    if pretrained and pretrained_path:
        model = load_pretrained_weights(model, pretrained_path)
    
    return model


def load_pretrained_weights(model, pretrained_path):
    """加载离线预训练权重"""
    try:
        if os.path.isfile(pretrained_path):
            logger.info(f"Loading pretrained weights from {pretrained_path}")
            checkpoint = torch.load(pretrained_path, map_location='cpu')
            
            # 处理不同的checkpoint格式
            if 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
            elif 'model' in checkpoint:
                state_dict = checkpoint['model']
            else:
                state_dict = checkpoint
            
            # 移除不匹配的键
            model_dict = model.state_dict()
            filtered_dict = {}
            
            for k, v in state_dict.items():
                # 移除模块名前缀
                if k.startswith('module.'):
                    k = k[7:]
                
                if k in model_dict and model_dict[k].shape == v.shape:
                    filtered_dict[k] = v
                else:
                    logger.warning(f"Skipping layer {k} due to shape mismatch or not found")
            
            model_dict.update(filtered_dict)
            model.load_state_dict(model_dict)
            logger.info(f"Successfully loaded {len(filtered_dict)} layers from pretrained weights")
            
        else:
            logger.warning(f"Pretrained weights file not found: {pretrained_path}")
            
    except Exception as e:
        logger.error(f"Error loading pretrained weights: {str(e)}")
    
    return model


def create_model(model_name: str, num_classes: int, pretrained: bool = False, 
                pretrained_path: Optional[str] = None, **kwargs):
    """创建模型的工厂函数"""
    
    model_dict = {
        'resnet18': resnet18_3d,
        'resnet34': resnet34_3d,
        'resnet50': resnet50_3d,
    }
    
    if model_name not in model_dict:
        raise ValueError(f"Model {model_name} not supported. Available models: {list(model_dict.keys())}")
    
    model = model_dict[model_name](
        num_classes=num_classes,
        pretrained=pretrained,
        pretrained_path=pretrained_path,
        **kwargs
    )
    
    return model


class ModelManager:
    """模型管理器，处理模型的保存和加载"""
    
    @staticmethod
    def save_checkpoint(model, optimizer, epoch, loss, accuracy, filepath):
        """保存模型检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'loss': loss,
            'accuracy': accuracy,
        }
        torch.save(checkpoint, filepath)
        logger.info(f"Checkpoint saved to {filepath}")
    
    @staticmethod
    def load_checkpoint(model, optimizer, filepath):
        """加载模型检查点"""
        if os.path.isfile(filepath):
            checkpoint = torch.load(filepath)
            model.load_state_dict(checkpoint['model_state_dict'])
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            epoch = checkpoint['epoch']
            loss = checkpoint['loss']
            accuracy = checkpoint['accuracy']
            logger.info(f"Checkpoint loaded from {filepath}")
            return epoch, loss, accuracy
        else:
            logger.warning(f"No checkpoint found at {filepath}")
            return 0, float('inf'), 0.0
    
    @staticmethod
    def save_model(model, filepath):
        """保存模型权重"""
        torch.save(model.state_dict(), filepath)
        logger.info(f"Model saved to {filepath}")
    
    @staticmethod
    def load_model(model, filepath):
        """加载模型权重"""
        if os.path.isfile(filepath):
            model.load_state_dict(torch.load(filepath))
            logger.info(f"Model loaded from {filepath}")
        else:
            logger.warning(f"No model found at {filepath}")
        return model
