#!/usr/bin/env python3
"""
3D ResNet50 医学影像分类训练脚本

使用方法:
    python src/train.py

功能:
- 自动发现训练数据中的类别
- 支持NII.gz格式的医学影像
- 3D ResNet50模型训练
- 离线预训练模型加载
- 完整的训练、验证和测试流程
"""

import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.config import Config
from src.dataset import create_data_loaders, auto_discover_classes
from src.model import create_model
from src.trainer import Trainer
from utils.logger import setup_logger, log_system_info, log_config, log_model_info, log_dataset_info


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='3D ResNet50 医学影像分类训练')

    parser.add_argument('--config', type=str, default=None,
                       help='配置文件路径 (可选)')
    parser.add_argument('--data-root', type=str, default=None,
                       help='数据根目录路径')
    parser.add_argument('--model-name', type=str, default='resnet50',
                       choices=['resnet18', 'resnet34', 'resnet50'],
                       help='模型名称')
    parser.add_argument('--batch-size', type=int, default=None,
                       help='批次大小')
    parser.add_argument('--epochs', type=int, default=None,
                       help='训练轮数')
    parser.add_argument('--lr', type=float, default=None,
                       help='学习率')
    parser.add_argument('--pretrained-path', type=str, default=None,
                       help='预训练模型路径')
    parser.add_argument('--resume', type=str, default=None,
                       help='恢复训练的检查点路径')
    parser.add_argument('--test-only', action='store_true',
                       help='仅进行测试，不训练')
    parser.add_argument('--device', type=str, default=None,
                       choices=['cpu', 'cuda'],
                       help='训练设备')

    return parser.parse_args()


def update_config_from_args(config: Config, args):
    """根据命令行参数更新配置"""
    if args.data_root:
        config.DATA_ROOT = Path(args.data_root)
        config.TRAIN_DIR = config.DATA_ROOT / "train"
        config.VAL_DIR = config.DATA_ROOT / "val"
        config.TEST_DIR = config.DATA_ROOT / "test"

    if args.model_name:
        config.MODEL_NAME = args.model_name

    if args.batch_size:
        config.BATCH_SIZE = args.batch_size

    if args.epochs:
        config.NUM_EPOCHS = args.epochs

    if args.lr:
        config.LEARNING_RATE = args.lr

    if args.pretrained_path:
        config.PRETRAINED_PATH = args.pretrained_path

    if args.device:
        config.DEVICE = args.device


def check_data_availability(config: Config, logger):
    """检查数据可用性"""
    logger.info("Checking data availability...")

    # 检查训练数据
    train_classes = auto_discover_classes(config.TRAIN_DIR)
    val_classes = auto_discover_classes(config.VAL_DIR)
    test_classes = auto_discover_classes(config.TEST_DIR)

    logger.info(f"Training classes found: {train_classes}")
    logger.info(f"Validation classes found: {val_classes}")
    logger.info(f"Test classes found: {test_classes}")

    if not train_classes:
        logger.warning("No training classes found! Please ensure data is properly organized.")
        logger.warning("Expected structure:")
        logger.warning("  data/medical/train/class1/*.nii.gz")
        logger.warning("  data/medical/train/class2/*.nii.gz")
        logger.warning("  ...")
        return False

    if not val_classes:
        logger.warning("No validation classes found! Using training data for validation.")

    if not test_classes:
        logger.warning("No test classes found! Testing will be skipped.")

    return True


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()

    # 设置配置
    config = Config()
    update_config_from_args(config, args)

    # 创建必要的目录
    config.create_directories()

    # 设置日志
    logger = setup_logger('3D_ResNet_Training', config.LOG_LEVEL)

    # 记录系统信息
    log_system_info(logger)
    log_config(logger, config)

    # 检查数据可用性
    if not check_data_availability(config, logger):
        if not args.test_only:
            logger.error("Cannot proceed with training due to missing data.")
            return

    try:
        # 创建数据加载器
        logger.info("Creating data loaders...")
        train_loader, val_loader, test_loader = create_data_loaders(config)

        # 记录数据集信息
        if len(train_loader.dataset) > 0:
            log_dataset_info(logger, train_loader, val_loader, test_loader)

        # 创建模型
        logger.info(f"Creating {config.MODEL_NAME} model...")
        model = create_model(
            model_name=config.MODEL_NAME,
            num_classes=config.NUM_CLASSES or 2,  # 默认二分类
            pretrained=config.PRETRAINED,
            pretrained_path=config.PRETRAINED_PATH if config.PRETRAINED else None,
            in_channels=1  # 医学影像通常是单通道
        )

        # 记录模型信息
        log_model_info(logger, model)

        # 创建训练器
        trainer = Trainer(model, train_loader, val_loader, test_loader, config)

        # 恢复训练（如果指定）
        if args.resume:
            logger.info(f"Resuming training from {args.resume}")
            trainer.model_manager.load_checkpoint(model, trainer.optimizer, args.resume)

        if args.test_only:
            # 仅测试
            logger.info("Running test only...")
            if len(test_loader.dataset) > 0:
                test_results = trainer.test()
                logger.info("Test completed successfully!")
            else:
                logger.warning("No test data available for testing.")
        else:
            # 训练
            if len(train_loader.dataset) > 0:
                logger.info("Starting training...")
                trainer.train()

                # 测试
                if len(test_loader.dataset) > 0:
                    logger.info("Starting testing...")
                    test_results = trainer.test()
                    logger.info("Training and testing completed successfully!")
                else:
                    logger.info("Training completed successfully! (No test data available)")
            else:
                logger.error("No training data available. Please check your data directory.")

    except Exception as e:
        logger.error(f"An error occurred: {str(e)}")
        logger.exception("Full traceback:")
        return 1

    logger.info("Program completed successfully!")
    return 0


if __name__ == "__main__":
    exit_code = main()