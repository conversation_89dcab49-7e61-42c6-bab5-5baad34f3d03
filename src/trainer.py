import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
import numpy as np
from tqdm import tqdm
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import time
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

from config.config import Config
from src.model import ModelManager

logger = logging.getLogger(__name__)

class Trainer:
    """训练器类，负责模型的训练、验证和测试"""
    
    def __init__(self, model, train_loader, val_loader, test_loader, config: Config):
        self.model = model
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.test_loader = test_loader
        self.config = config
        
        # 设置设备
        self.device = torch.device(config.DEVICE)
        self.model.to(self.device)
        
        # 设置损失函数和优化器
        self.criterion = nn.CrossEntropyLoss()
        self.optimizer = optim.SGD(
            self.model.parameters(),
            lr=config.LEARNING_RATE,
            momentum=config.MOMENTUM,
            weight_decay=config.WEIGHT_DECAY
        )
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.5, patience=10, verbose=True
        )
        
        # TensorBoard写入器
        self.writer = SummaryWriter(config.TENSORBOARD_DIR)
        
        # 训练状态
        self.current_epoch = 0
        self.best_val_acc = 0.0
        self.best_val_loss = float('inf')
        self.early_stopping_counter = 0
        
        # 模型管理器
        self.model_manager = ModelManager()
        
        # 训练历史
        self.train_history = {
            'loss': [],
            'accuracy': [],
            'val_loss': [],
            'val_accuracy': []
        }
    
    def train_epoch(self) -> Tuple[float, float]:
        """训练一个epoch"""
        self.model.train()
        running_loss = 0.0
        correct_predictions = 0
        total_samples = 0
        
        progress_bar = tqdm(self.train_loader, desc=f'Epoch {self.current_epoch+1}/{self.config.NUM_EPOCHS}')
        
        for batch_idx, (data, targets) in enumerate(progress_bar):
            data, targets = data.to(self.device), targets.to(self.device)
            
            # 前向传播
            self.optimizer.zero_grad()
            outputs = self.model(data)
            loss = self.criterion(outputs, targets)
            
            # 反向传播
            loss.backward()
            self.optimizer.step()
            
            # 统计
            running_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total_samples += targets.size(0)
            correct_predictions += (predicted == targets).sum().item()
            
            # 更新进度条
            current_acc = 100. * correct_predictions / total_samples
            progress_bar.set_postfix({
                'Loss': f'{running_loss/(batch_idx+1):.4f}',
                'Acc': f'{current_acc:.2f}%'
            })
        
        epoch_loss = running_loss / len(self.train_loader)
        epoch_acc = 100. * correct_predictions / total_samples
        
        return epoch_loss, epoch_acc
    
    def validate_epoch(self) -> Tuple[float, float]:
        """验证一个epoch"""
        self.model.eval()
        running_loss = 0.0
        correct_predictions = 0
        total_samples = 0
        
        with torch.no_grad():
            for data, targets in tqdm(self.val_loader, desc='Validation'):
                data, targets = data.to(self.device), targets.to(self.device)
                
                outputs = self.model(data)
                loss = self.criterion(outputs, targets)
                
                running_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                total_samples += targets.size(0)
                correct_predictions += (predicted == targets).sum().item()
        
        epoch_loss = running_loss / len(self.val_loader)
        epoch_acc = 100. * correct_predictions / total_samples
        
        return epoch_loss, epoch_acc
    
    def train(self):
        """完整的训练流程"""
        logger.info("Starting training...")
        logger.info(f"Training on device: {self.device}")
        logger.info(f"Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
        
        start_time = time.time()
        
        for epoch in range(self.config.NUM_EPOCHS):
            self.current_epoch = epoch
            
            # 训练
            train_loss, train_acc = self.train_epoch()
            
            # 验证
            if epoch % self.config.VALIDATE_EVERY_N_EPOCHS == 0:
                val_loss, val_acc = self.validate_epoch()
                
                # 更新学习率
                self.scheduler.step(val_loss)
                
                # 记录历史
                self.train_history['loss'].append(train_loss)
                self.train_history['accuracy'].append(train_acc)
                self.train_history['val_loss'].append(val_loss)
                self.train_history['val_accuracy'].append(val_acc)
                
                # TensorBoard记录
                self.writer.add_scalar('Loss/Train', train_loss, epoch)
                self.writer.add_scalar('Loss/Validation', val_loss, epoch)
                self.writer.add_scalar('Accuracy/Train', train_acc, epoch)
                self.writer.add_scalar('Accuracy/Validation', val_acc, epoch)
                self.writer.add_scalar('Learning_Rate', self.optimizer.param_groups[0]['lr'], epoch)
                
                # 打印结果
                logger.info(f'Epoch [{epoch+1}/{self.config.NUM_EPOCHS}]')
                logger.info(f'Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%')
                logger.info(f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%')
                
                # 保存最佳模型
                if val_acc > self.best_val_acc:
                    self.best_val_acc = val_acc
                    self.best_val_loss = val_loss
                    self.early_stopping_counter = 0
                    
                    # 保存最佳模型
                    best_model_path = self.config.MODEL_SAVE_DIR / 'best_model.pth'
                    self.model_manager.save_model(self.model, best_model_path)
                    logger.info(f'New best model saved with validation accuracy: {val_acc:.2f}%')
                else:
                    self.early_stopping_counter += 1
                
                # 早停检查
                if self.early_stopping_counter >= self.config.EARLY_STOPPING_PATIENCE:
                    logger.info(f'Early stopping triggered after {epoch+1} epochs')
                    break
            
            # 定期保存检查点
            if (epoch + 1) % self.config.SAVE_EVERY_N_EPOCHS == 0:
                checkpoint_path = self.config.MODEL_SAVE_DIR / f'checkpoint_epoch_{epoch+1}.pth'
                self.model_manager.save_checkpoint(
                    self.model, self.optimizer, epoch, train_loss, train_acc, checkpoint_path
                )
        
        # 训练完成
        total_time = time.time() - start_time
        logger.info(f'Training completed in {total_time/3600:.2f} hours')
        logger.info(f'Best validation accuracy: {self.best_val_acc:.2f}%')
        
        # 保存最终模型
        final_model_path = self.config.MODEL_SAVE_DIR / 'final_model.pth'
        self.model_manager.save_model(self.model, final_model_path)
        
        # 保存训练历史
        self.save_training_plots()
        
        self.writer.close()
    
    def test(self) -> Dict[str, float]:
        """测试模型"""
        logger.info("Starting testing...")
        
        # 加载最佳模型
        best_model_path = self.config.MODEL_SAVE_DIR / 'best_model.pth'
        if best_model_path.exists():
            self.model_manager.load_model(self.model, best_model_path)
            logger.info("Loaded best model for testing")
        
        self.model.eval()
        test_loss = 0.0
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for data, targets in tqdm(self.test_loader, desc='Testing'):
                data, targets = data.to(self.device), targets.to(self.device)
                
                outputs = self.model(data)
                loss = self.criterion(outputs, targets)
                
                test_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                
                all_predictions.extend(predicted.cpu().numpy())
                all_targets.extend(targets.cpu().numpy())
        
        # 计算指标
        test_loss /= len(self.test_loader)
        test_acc = accuracy_score(all_targets, all_predictions) * 100
        precision, recall, f1, _ = precision_recall_fscore_support(
            all_targets, all_predictions, average='weighted'
        )
        
        # 混淆矩阵
        cm = confusion_matrix(all_targets, all_predictions)
        
        results = {
            'test_loss': test_loss,
            'test_accuracy': test_acc,
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'confusion_matrix': cm
        }
        
        logger.info(f'Test Results:')
        logger.info(f'Test Loss: {test_loss:.4f}')
        logger.info(f'Test Accuracy: {test_acc:.2f}%')
        logger.info(f'Precision: {precision:.4f}')
        logger.info(f'Recall: {recall:.4f}')
        logger.info(f'F1 Score: {f1:.4f}')
        
        # 保存混淆矩阵图
        self.save_confusion_matrix(cm)
        
        return results
    
    def save_training_plots(self):
        """保存训练过程图表"""
        fig, ((ax1, ax2)) = plt.subplots(1, 2, figsize=(15, 5))
        
        # 损失曲线
        epochs = range(1, len(self.train_history['loss']) + 1)
        ax1.plot(epochs, self.train_history['loss'], 'b-', label='Training Loss')
        ax1.plot(epochs, self.train_history['val_loss'], 'r-', label='Validation Loss')
        ax1.set_title('Training and Validation Loss')
        ax1.set_xlabel('Epochs')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True)
        
        # 准确率曲线
        ax2.plot(epochs, self.train_history['accuracy'], 'b-', label='Training Accuracy')
        ax2.plot(epochs, self.train_history['val_accuracy'], 'r-', label='Validation Accuracy')
        ax2.set_title('Training and Validation Accuracy')
        ax2.set_xlabel('Epochs')
        ax2.set_ylabel('Accuracy (%)')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig(self.config.OUTPUT_DIR / 'training_history.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info("Training plots saved")
    
    def save_confusion_matrix(self, cm):
        """保存混淆矩阵图"""
        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
        plt.title('Confusion Matrix')
        plt.ylabel('True Label')
        plt.xlabel('Predicted Label')
        plt.savefig(self.config.OUTPUT_DIR / 'confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info("Confusion matrix saved")
