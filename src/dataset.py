import os
import glob
from pathlib import Path
from typing import List, Tuple, Optional, Dict, Any
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
import SimpleITK as sitk
from scipy import ndimage
import logging
from config.config import Config

logger = logging.getLogger(__name__)

class MedicalImageDataset(Dataset):
    """医学影像数据集类，支持NII.gz文件的自动发现和加载"""
    
    def __init__(self, 
                 data_dir: str, 
                 transform=None, 
                 target_size: Tuple[int, int, int] = None,
                 spacing: Tuple[float, float, float] = None,
                 intensity_range: Tuple[float, float] = None):
        """
        初始化数据集
        
        Args:
            data_dir: 数据目录路径
            transform: 数据变换函数
            target_size: 目标图像尺寸 (D, H, W)
            spacing: 重采样间距 (z, y, x)
            intensity_range: 强度值范围 (min, max)
        """
        self.data_dir = Path(data_dir)
        self.transform = transform
        self.target_size = target_size or Config.IMAGE_SIZE
        self.spacing = spacing or Config.SPACING
        self.intensity_range = intensity_range or Config.INTENSITY_RANGE
        
        # 自动发现类别和文件
        self.classes, self.class_to_idx = self._find_classes()
        self.samples = self._make_dataset()
        
        logger.info(f"Found {len(self.classes)} classes: {self.classes}")
        logger.info(f"Found {len(self.samples)} samples in {data_dir}")
    
    def _find_classes(self) -> Tuple[List[str], Dict[str, int]]:
        """自动发现数据目录中的类别"""
        if not self.data_dir.exists():
            logger.warning(f"Data directory {self.data_dir} does not exist")
            return [], {}
        
        classes = [d.name for d in self.data_dir.iterdir() 
                  if d.is_dir() and not d.name.startswith('.')]
        classes.sort()
        class_to_idx = {cls_name: i for i, cls_name in enumerate(classes)}
        
        return classes, class_to_idx
    
    def _make_dataset(self) -> List[Tuple[str, int]]:
        """创建数据集样本列表"""
        samples = []
        
        for class_name in self.classes:
            class_dir = self.data_dir / class_name
            class_idx = self.class_to_idx[class_name]
            
            # 查找所有.nii.gz文件
            nii_files = list(class_dir.glob("*.nii.gz"))
            nii_files.extend(class_dir.glob("*.nii"))
            
            for file_path in nii_files:
                samples.append((str(file_path), class_idx))
        
        return samples
    
    def _load_nii_image(self, file_path: str) -> np.ndarray:
        """加载NII图像文件"""
        try:
            # 使用SimpleITK加载图像
            image = sitk.ReadImage(file_path)
            image_array = sitk.GetArrayFromImage(image)
            
            # 获取原始spacing
            original_spacing = image.GetSpacing()[::-1]  # ITK使用(x,y,z)，我们需要(z,y,x)
            
            # 重采样到目标spacing
            if self.spacing != original_spacing:
                image_array = self._resample_image(image_array, original_spacing, self.spacing)
            
            # 调整到目标尺寸
            image_array = self._resize_image(image_array, self.target_size)
            
            # 强度归一化
            image_array = self._normalize_intensity(image_array)
            
            return image_array.astype(np.float32)
            
        except Exception as e:
            logger.error(f"Error loading image {file_path}: {str(e)}")
            # 返回零填充的图像作为fallback
            return np.zeros(self.target_size, dtype=np.float32)
    
    def _resample_image(self, image: np.ndarray, 
                       original_spacing: Tuple[float, float, float],
                       target_spacing: Tuple[float, float, float]) -> np.ndarray:
        """重采样图像到目标spacing"""
        resize_factor = np.array(original_spacing) / np.array(target_spacing)
        new_shape = np.round(np.array(image.shape) * resize_factor).astype(int)
        
        # 使用scipy进行重采样
        resampled_image = ndimage.zoom(image, resize_factor, order=1)
        
        return resampled_image
    
    def _resize_image(self, image: np.ndarray, target_size: Tuple[int, int, int]) -> np.ndarray:
        """调整图像到目标尺寸"""
        current_size = image.shape
        
        # 计算缩放因子
        zoom_factors = [target_size[i] / current_size[i] for i in range(3)]
        
        # 使用scipy进行缩放
        resized_image = ndimage.zoom(image, zoom_factors, order=1)
        
        return resized_image
    
    def _normalize_intensity(self, image: np.ndarray) -> np.ndarray:
        """强度归一化"""
        # 裁剪到指定范围
        image = np.clip(image, self.intensity_range[0], self.intensity_range[1])
        
        # 归一化到[0, 1]
        image = (image - self.intensity_range[0]) / (self.intensity_range[1] - self.intensity_range[0])
        
        # 标准化到[-1, 1]
        image = 2.0 * image - 1.0
        
        return image
    
    def __len__(self) -> int:
        return len(self.samples)
    
    def __getitem__(self, index: int) -> Tuple[torch.Tensor, int]:
        """获取数据样本"""
        file_path, label = self.samples[index]
        
        # 加载图像
        image = self._load_nii_image(file_path)
        
        # 添加通道维度 (C, D, H, W)
        image = np.expand_dims(image, axis=0)
        
        # 转换为tensor
        image = torch.from_numpy(image)
        
        # 应用变换
        if self.transform:
            image = self.transform(image)
        
        return image, label
    
    def get_class_weights(self) -> torch.Tensor:
        """计算类别权重，用于处理不平衡数据"""
        class_counts = [0] * len(self.classes)
        
        for _, label in self.samples:
            class_counts[label] += 1
        
        total_samples = len(self.samples)
        class_weights = [total_samples / (len(self.classes) * count) for count in class_counts]
        
        return torch.FloatTensor(class_weights)


def create_data_loaders(config: Config) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """创建训练、验证和测试数据加载器"""
    
    # 创建数据集
    train_dataset = MedicalImageDataset(
        data_dir=config.TRAIN_DIR,
        target_size=config.IMAGE_SIZE,
        spacing=config.SPACING,
        intensity_range=config.INTENSITY_RANGE
    )
    
    val_dataset = MedicalImageDataset(
        data_dir=config.VAL_DIR,
        target_size=config.IMAGE_SIZE,
        spacing=config.SPACING,
        intensity_range=config.INTENSITY_RANGE
    )
    
    test_dataset = MedicalImageDataset(
        data_dir=config.TEST_DIR,
        target_size=config.IMAGE_SIZE,
        spacing=config.SPACING,
        intensity_range=config.INTENSITY_RANGE
    )
    
    # 更新配置中的类别数量
    if len(train_dataset.classes) > 0:
        config.update_num_classes(len(train_dataset.classes))
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config.BATCH_SIZE,
        shuffle=True,
        num_workers=config.NUM_WORKERS,
        pin_memory=config.PIN_MEMORY
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config.BATCH_SIZE,
        shuffle=False,
        num_workers=config.NUM_WORKERS,
        pin_memory=config.PIN_MEMORY
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config.BATCH_SIZE,
        shuffle=False,
        num_workers=config.NUM_WORKERS,
        pin_memory=config.PIN_MEMORY
    )
    
    return train_loader, val_loader, test_loader


def auto_discover_classes(data_dir: str) -> List[str]:
    """自动发现数据目录中的类别"""
    data_path = Path(data_dir)
    if not data_path.exists():
        return []
    
    classes = [d.name for d in data_path.iterdir() 
              if d.is_dir() and not d.name.startswith('.')]
    classes.sort()
    
    return classes
