#!/usr/bin/env python3
"""
3D ResNet50 医学影像分类推理脚本

使用方法:
    python src/inference.py --model-path outputs/models/best_model.pth --image-path path/to/image.nii.gz

功能:
- 加载训练好的模型
- 对单个NII.gz文件进行预测
- 输出预测结果和置信度
"""

import sys
import argparse
from pathlib import Path
import torch
import numpy as np

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.config import Config
from src.model import create_model
from src.dataset import MedicalImageDataset
from utils.logger import setup_logger


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='3D ResNet50 医学影像分类推理')
    
    parser.add_argument('--model-path', type=str, required=True,
                       help='训练好的模型权重路径')
    parser.add_argument('--image-path', type=str, required=True,
                       help='待预测的NII.gz图像路径')
    parser.add_argument('--config-path', type=str, default=None,
                       help='配置文件路径 (可选)')
    parser.add_argument('--num-classes', type=int, default=2,
                       help='类别数量')
    parser.add_argument('--model-name', type=str, default='resnet50',
                       choices=['resnet18', 'resnet34', 'resnet50'],
                       help='模型名称')
    parser.add_argument('--device', type=str, default='cuda',
                       choices=['cpu', 'cuda'],
                       help='推理设备')
    
    return parser.parse_args()


class MedicalImageInference:
    """医学影像推理类"""
    
    def __init__(self, model_path: str, num_classes: int, model_name: str = 'resnet50', device: str = 'cuda'):
        self.device = torch.device(device if torch.cuda.is_available() else 'cpu')
        self.num_classes = num_classes
        
        # 创建模型
        self.model = create_model(
            model_name=model_name,
            num_classes=num_classes,
            pretrained=False,
            in_channels=1
        )
        
        # 加载模型权重
        self.load_model(model_path)
        self.model.to(self.device)
        self.model.eval()
        
        # 创建数据集实例用于预处理
        self.dataset = MedicalImageDataset(
            data_dir="dummy",  # 不会实际使用
            target_size=Config.IMAGE_SIZE,
            spacing=Config.SPACING,
            intensity_range=Config.INTENSITY_RANGE
        )
    
    def load_model(self, model_path: str):
        """加载模型权重"""
        if not Path(model_path).exists():
            raise FileNotFoundError(f"Model file not found: {model_path}")
        
        checkpoint = torch.load(model_path, map_location='cpu')
        
        # 处理不同的保存格式
        if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
            state_dict = checkpoint['model_state_dict']
        else:
            state_dict = checkpoint
        
        self.model.load_state_dict(state_dict)
        print(f"Model loaded from {model_path}")
    
    def preprocess_image(self, image_path: str) -> torch.Tensor:
        """预处理单个图像"""
        # 使用数据集的预处理方法
        image = self.dataset._load_nii_image(image_path)
        
        # 添加批次和通道维度 (1, 1, D, H, W)
        image = np.expand_dims(image, axis=0)  # 添加通道维度
        image = torch.from_numpy(image).unsqueeze(0)  # 添加批次维度
        
        return image
    
    def predict(self, image_path: str) -> tuple:
        """对单个图像进行预测"""
        # 预处理图像
        image = self.preprocess_image(image_path)
        image = image.to(self.device)
        
        # 推理
        with torch.no_grad():
            outputs = self.model(image)
            probabilities = torch.softmax(outputs, dim=1)
            predicted_class = torch.argmax(probabilities, dim=1).item()
            confidence = probabilities[0, predicted_class].item()
        
        return predicted_class, confidence, probabilities[0].cpu().numpy()
    
    def predict_batch(self, image_paths: list) -> list:
        """批量预测"""
        results = []
        
        for image_path in image_paths:
            try:
                predicted_class, confidence, probabilities = self.predict(image_path)
                results.append({
                    'image_path': image_path,
                    'predicted_class': predicted_class,
                    'confidence': confidence,
                    'probabilities': probabilities
                })
            except Exception as e:
                print(f"Error processing {image_path}: {str(e)}")
                results.append({
                    'image_path': image_path,
                    'error': str(e)
                })
        
        return results


def main():
    """主函数"""
    args = parse_arguments()
    
    # 设置日志
    logger = setup_logger('3D_ResNet_Inference')
    
    try:
        # 创建推理器
        logger.info("Initializing inference engine...")
        inference_engine = MedicalImageInference(
            model_path=args.model_path,
            num_classes=args.num_classes,
            model_name=args.model_name,
            device=args.device
        )
        
        # 检查图像文件
        image_path = Path(args.image_path)
        if not image_path.exists():
            logger.error(f"Image file not found: {image_path}")
            return 1
        
        # 进行预测
        logger.info(f"Predicting image: {image_path}")
        predicted_class, confidence, probabilities = inference_engine.predict(str(image_path))
        
        # 输出结果
        logger.info("="*50)
        logger.info("PREDICTION RESULTS")
        logger.info("="*50)
        logger.info(f"Image: {image_path}")
        logger.info(f"Predicted Class: {predicted_class}")
        logger.info(f"Confidence: {confidence:.4f} ({confidence*100:.2f}%)")
        logger.info("Class Probabilities:")
        for i, prob in enumerate(probabilities):
            logger.info(f"  Class {i}: {prob:.4f} ({prob*100:.2f}%)")
        logger.info("="*50)
        
    except Exception as e:
        logger.error(f"An error occurred: {str(e)}")
        logger.exception("Full traceback:")
        return 1
    
    logger.info("Inference completed successfully!")
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
