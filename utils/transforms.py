import torch
import numpy as np
from scipy import ndimage
import random
from typing import Tuple, Optional

class RandomRotation3D:
    """3D随机旋转变换"""
    
    def __init__(self, degrees: float = 15):
        self.degrees = degrees
    
    def __call__(self, image: torch.Tensor) -> torch.Tensor:
        """
        Args:
            image: 输入图像张量，形状为 (C, D, H, W)
        """
        if random.random() < 0.5:
            return image
        
        # 转换为numpy
        image_np = image.numpy()
        
        # 随机旋转角度
        angle_x = random.uniform(-self.degrees, self.degrees)
        angle_y = random.uniform(-self.degrees, self.degrees)
        angle_z = random.uniform(-self.degrees, self.degrees)
        
        # 对每个通道进行旋转
        rotated_channels = []
        for c in range(image_np.shape[0]):
            channel = image_np[c]
            
            # 绕x轴旋转
            if angle_x != 0:
                channel = ndimage.rotate(channel, angle_x, axes=(1, 2), reshape=False, order=1)
            
            # 绕y轴旋转
            if angle_y != 0:
                channel = ndimage.rotate(channel, angle_y, axes=(0, 2), reshape=False, order=1)
            
            # 绕z轴旋转
            if angle_z != 0:
                channel = ndimage.rotate(channel, angle_z, axes=(0, 1), reshape=False, order=1)
            
            rotated_channels.append(channel)
        
        rotated_image = np.stack(rotated_channels, axis=0)
        return torch.from_numpy(rotated_image.astype(np.float32))


class RandomTranslation3D:
    """3D随机平移变换"""
    
    def __init__(self, translation_range: float = 0.1):
        self.translation_range = translation_range
    
    def __call__(self, image: torch.Tensor) -> torch.Tensor:
        """
        Args:
            image: 输入图像张量，形状为 (C, D, H, W)
        """
        if random.random() < 0.5:
            return image
        
        image_np = image.numpy()
        _, d, h, w = image_np.shape
        
        # 计算平移距离
        shift_d = int(d * random.uniform(-self.translation_range, self.translation_range))
        shift_h = int(h * random.uniform(-self.translation_range, self.translation_range))
        shift_w = int(w * random.uniform(-self.translation_range, self.translation_range))
        
        # 对每个通道进行平移
        translated_channels = []
        for c in range(image_np.shape[0]):
            channel = image_np[c]
            translated_channel = ndimage.shift(channel, (shift_d, shift_h, shift_w), order=1)
            translated_channels.append(translated_channel)
        
        translated_image = np.stack(translated_channels, axis=0)
        return torch.from_numpy(translated_image.astype(np.float32))


class RandomScaling3D:
    """3D随机缩放变换"""
    
    def __init__(self, scaling_range: float = 0.1):
        self.scaling_range = scaling_range
    
    def __call__(self, image: torch.Tensor) -> torch.Tensor:
        """
        Args:
            image: 输入图像张量，形状为 (C, D, H, W)
        """
        if random.random() < 0.5:
            return image
        
        image_np = image.numpy()
        
        # 随机缩放因子
        scale_factor = random.uniform(1 - self.scaling_range, 1 + self.scaling_range)
        
        # 对每个通道进行缩放
        scaled_channels = []
        for c in range(image_np.shape[0]):
            channel = image_np[c]
            scaled_channel = ndimage.zoom(channel, scale_factor, order=1)
            
            # 裁剪或填充到原始尺寸
            original_shape = channel.shape
            scaled_shape = scaled_channel.shape
            
            if scale_factor > 1:
                # 缩放后图像更大，需要裁剪
                start_d = (scaled_shape[0] - original_shape[0]) // 2
                start_h = (scaled_shape[1] - original_shape[1]) // 2
                start_w = (scaled_shape[2] - original_shape[2]) // 2
                
                scaled_channel = scaled_channel[
                    start_d:start_d + original_shape[0],
                    start_h:start_h + original_shape[1],
                    start_w:start_w + original_shape[2]
                ]
            else:
                # 缩放后图像更小，需要填充
                pad_d = (original_shape[0] - scaled_shape[0]) // 2
                pad_h = (original_shape[1] - scaled_shape[1]) // 2
                pad_w = (original_shape[2] - scaled_shape[2]) // 2
                
                scaled_channel = np.pad(
                    scaled_channel,
                    ((pad_d, original_shape[0] - scaled_shape[0] - pad_d),
                     (pad_h, original_shape[1] - scaled_shape[1] - pad_h),
                     (pad_w, original_shape[2] - scaled_shape[2] - pad_w)),
                    mode='constant',
                    constant_values=0
                )
            
            scaled_channels.append(scaled_channel)
        
        scaled_image = np.stack(scaled_channels, axis=0)
        return torch.from_numpy(scaled_image.astype(np.float32))


class RandomFlip3D:
    """3D随机翻转变换"""
    
    def __init__(self, flip_probability: float = 0.5):
        self.flip_probability = flip_probability
    
    def __call__(self, image: torch.Tensor) -> torch.Tensor:
        """
        Args:
            image: 输入图像张量，形状为 (C, D, H, W)
        """
        image_np = image.numpy()
        
        # 随机翻转各个轴
        if random.random() < self.flip_probability:
            image_np = np.flip(image_np, axis=1)  # 翻转D轴
        
        if random.random() < self.flip_probability:
            image_np = np.flip(image_np, axis=2)  # 翻转H轴
        
        if random.random() < self.flip_probability:
            image_np = np.flip(image_np, axis=3)  # 翻转W轴
        
        return torch.from_numpy(image_np.copy().astype(np.float32))


class RandomNoise3D:
    """3D随机噪声变换"""
    
    def __init__(self, noise_std: float = 0.1):
        self.noise_std = noise_std
    
    def __call__(self, image: torch.Tensor) -> torch.Tensor:
        """
        Args:
            image: 输入图像张量，形状为 (C, D, H, W)
        """
        if random.random() < 0.5:
            return image
        
        noise = torch.randn_like(image) * self.noise_std
        noisy_image = image + noise
        
        # 确保值在合理范围内
        noisy_image = torch.clamp(noisy_image, -1, 1)
        
        return noisy_image


class Compose3D:
    """组合多个3D变换"""
    
    def __init__(self, transforms):
        self.transforms = transforms
    
    def __call__(self, image: torch.Tensor) -> torch.Tensor:
        for transform in self.transforms:
            image = transform(image)
        return image


def get_training_transforms(config):
    """获取训练时的数据变换"""
    transforms = []
    
    if config.USE_AUGMENTATION:
        transforms.extend([
            RandomRotation3D(degrees=config.ROTATION_RANGE),
            RandomTranslation3D(translation_range=config.TRANSLATION_RANGE),
            RandomScaling3D(scaling_range=config.SCALING_RANGE),
            RandomFlip3D(flip_probability=0.5),
            RandomNoise3D(noise_std=0.05)
        ])
    
    return Compose3D(transforms)


def get_validation_transforms():
    """获取验证时的数据变换（通常不包含随机变换）"""
    return Compose3D([])  # 验证时不使用数据增强
