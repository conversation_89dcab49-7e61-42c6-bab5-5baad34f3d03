#!/usr/bin/env python3
"""
数据准备工具

功能:
- 检查数据目录结构
- 验证NII.gz文件
- 数据集统计
- 数据集划分
"""

import os
import shutil
from pathlib import Path
from typing import List, Dict, Tuple
import random
import logging
from collections import defaultdict

import SimpleITK as sitk
import numpy as np

logger = logging.getLogger(__name__)


def check_nii_file(file_path: str) -> Dict:
    """检查NII文件的基本信息"""
    try:
        image = sitk.ReadImage(file_path)
        
        info = {
            'path': file_path,
            'size': image.GetSize(),  # (x, y, z)
            'spacing': image.GetSpacing(),  # (x, y, z)
            'origin': image.GetOrigin(),
            'direction': image.GetDirection(),
            'pixel_type': image.GetPixelIDTypeAsString(),
            'dimensions': image.GetDimension(),
            'file_size_mb': os.path.getsize(file_path) / (1024 * 1024),
            'valid': True,
            'error': None
        }
        
        # 获取数组信息
        array = sitk.GetArrayFromImage(image)
        info.update({
            'array_shape': array.shape,  # (z, y, x)
            'min_value': float(np.min(array)),
            'max_value': float(np.max(array)),
            'mean_value': float(np.mean(array)),
            'std_value': float(np.std(array))
        })
        
    except Exception as e:
        info = {
            'path': file_path,
            'valid': False,
            'error': str(e)
        }
    
    return info


def scan_data_directory(data_dir: str) -> Dict:
    """扫描数据目录，获取详细信息"""
    data_path = Path(data_dir)
    
    if not data_path.exists():
        return {'error': f"Directory {data_dir} does not exist"}
    
    results = {
        'directory': str(data_path),
        'classes': {},
        'total_files': 0,
        'valid_files': 0,
        'invalid_files': 0,
        'file_details': []
    }
    
    # 扫描每个类别目录
    for class_dir in data_path.iterdir():
        if not class_dir.is_dir() or class_dir.name.startswith('.'):
            continue
        
        class_name = class_dir.name
        class_info = {
            'name': class_name,
            'path': str(class_dir),
            'files': [],
            'count': 0,
            'valid_count': 0,
            'invalid_count': 0
        }
        
        # 查找NII文件
        nii_files = list(class_dir.glob("*.nii.gz")) + list(class_dir.glob("*.nii"))
        
        for nii_file in nii_files:
            file_info = check_nii_file(str(nii_file))
            file_info['class'] = class_name
            
            class_info['files'].append(file_info)
            results['file_details'].append(file_info)
            
            if file_info['valid']:
                class_info['valid_count'] += 1
                results['valid_files'] += 1
            else:
                class_info['invalid_count'] += 1
                results['invalid_files'] += 1
        
        class_info['count'] = len(nii_files)
        results['total_files'] += class_info['count']
        results['classes'][class_name] = class_info
    
    return results


def print_data_summary(scan_results: Dict):
    """打印数据摘要"""
    print("="*60)
    print("DATA DIRECTORY SUMMARY")
    print("="*60)
    print(f"Directory: {scan_results['directory']}")
    print(f"Total files: {scan_results['total_files']}")
    print(f"Valid files: {scan_results['valid_files']}")
    print(f"Invalid files: {scan_results['invalid_files']}")
    print(f"Number of classes: {len(scan_results['classes'])}")
    print()
    
    # 类别详情
    print("CLASS DETAILS:")
    print("-" * 60)
    for class_name, class_info in scan_results['classes'].items():
        print(f"Class: {class_name}")
        print(f"  Total files: {class_info['count']}")
        print(f"  Valid files: {class_info['valid_count']}")
        print(f"  Invalid files: {class_info['invalid_count']}")
        
        if class_info['valid_count'] > 0:
            # 计算统计信息
            valid_files = [f for f in class_info['files'] if f['valid']]
            
            if valid_files:
                sizes = [f['array_shape'] for f in valid_files]
                spacings = [f['spacing'] for f in valid_files]
                file_sizes = [f['file_size_mb'] for f in valid_files]
                
                print(f"  Image shapes: {set(sizes)}")
                print(f"  Spacings: {set(spacings)}")
                print(f"  File sizes: {min(file_sizes):.1f} - {max(file_sizes):.1f} MB")
        print()
    
    # 无效文件详情
    if scan_results['invalid_files'] > 0:
        print("INVALID FILES:")
        print("-" * 60)
        for file_info in scan_results['file_details']:
            if not file_info['valid']:
                print(f"  {file_info['path']}: {file_info['error']}")
        print()


def split_dataset(source_dir: str, train_ratio: float = 0.7, val_ratio: float = 0.15, 
                 test_ratio: float = 0.15, random_seed: int = 42) -> bool:
    """将数据集划分为训练、验证和测试集"""
    
    if abs(train_ratio + val_ratio + test_ratio - 1.0) > 1e-6:
        logger.error("Train, validation, and test ratios must sum to 1.0")
        return False
    
    source_path = Path(source_dir)
    if not source_path.exists():
        logger.error(f"Source directory {source_dir} does not exist")
        return False
    
    # 设置随机种子
    random.seed(random_seed)
    
    # 创建目标目录
    parent_dir = source_path.parent
    train_dir = parent_dir / "train"
    val_dir = parent_dir / "val"
    test_dir = parent_dir / "test"
    
    for dir_path in [train_dir, val_dir, test_dir]:
        dir_path.mkdir(exist_ok=True)
    
    # 处理每个类别
    for class_dir in source_path.iterdir():
        if not class_dir.is_dir() or class_dir.name.startswith('.'):
            continue
        
        class_name = class_dir.name
        logger.info(f"Processing class: {class_name}")
        
        # 创建类别目录
        for dir_path in [train_dir, val_dir, test_dir]:
            (dir_path / class_name).mkdir(exist_ok=True)
        
        # 获取所有文件
        nii_files = list(class_dir.glob("*.nii.gz")) + list(class_dir.glob("*.nii"))
        random.shuffle(nii_files)
        
        # 计算划分点
        total_files = len(nii_files)
        train_count = int(total_files * train_ratio)
        val_count = int(total_files * val_ratio)
        
        # 划分文件
        train_files = nii_files[:train_count]
        val_files = nii_files[train_count:train_count + val_count]
        test_files = nii_files[train_count + val_count:]
        
        # 复制文件
        for files, target_dir in [(train_files, train_dir), (val_files, val_dir), (test_files, test_dir)]:
            for file_path in files:
                target_path = target_dir / class_name / file_path.name
                shutil.copy2(file_path, target_path)
        
        logger.info(f"  Train: {len(train_files)}, Val: {len(val_files)}, Test: {len(test_files)}")
    
    logger.info("Dataset split completed successfully!")
    return True


def validate_dataset_structure(data_root: str) -> bool:
    """验证数据集结构是否正确"""
    data_path = Path(data_root)
    
    required_dirs = ['train', 'val', 'test']
    missing_dirs = []
    
    for dir_name in required_dirs:
        dir_path = data_path / dir_name
        if not dir_path.exists():
            missing_dirs.append(dir_name)
    
    if missing_dirs:
        logger.warning(f"Missing directories: {missing_dirs}")
        return False
    
    # 检查每个目录是否有类别子目录
    for dir_name in required_dirs:
        dir_path = data_path / dir_name
        class_dirs = [d for d in dir_path.iterdir() if d.is_dir() and not d.name.startswith('.')]
        
        if not class_dirs:
            logger.warning(f"No class directories found in {dir_path}")
            return False
        
        logger.info(f"{dir_name}: {len(class_dirs)} classes found")
    
    return True


def main():
    """主函数 - 用于命令行调用"""
    import argparse
    
    parser = argparse.ArgumentParser(description='数据准备工具')
    parser.add_argument('--scan', type=str, help='扫描数据目录')
    parser.add_argument('--split', type=str, help='划分数据集的源目录')
    parser.add_argument('--validate', type=str, help='验证数据集结构')
    parser.add_argument('--train-ratio', type=float, default=0.7, help='训练集比例')
    parser.add_argument('--val-ratio', type=float, default=0.15, help='验证集比例')
    parser.add_argument('--test-ratio', type=float, default=0.15, help='测试集比例')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    
    args = parser.parse_args()
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    if args.scan:
        print("Scanning data directory...")
        results = scan_data_directory(args.scan)
        print_data_summary(results)
    
    elif args.split:
        print("Splitting dataset...")
        success = split_dataset(
            args.split, 
            args.train_ratio, 
            args.val_ratio, 
            args.test_ratio, 
            args.seed
        )
        if success:
            print("Dataset split completed successfully!")
        else:
            print("Dataset split failed!")
    
    elif args.validate:
        print("Validating dataset structure...")
        is_valid = validate_dataset_structure(args.validate)
        if is_valid:
            print("Dataset structure is valid!")
        else:
            print("Dataset structure is invalid!")
    
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
