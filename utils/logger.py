import logging
import sys
from pathlib import Path
from datetime import datetime
from config.config import Config

def setup_logger(name: str = __name__, level: str = "INFO") -> logging.Logger:
    """设置日志记录器"""
    
    # 创建日志目录
    Config.create_directories()
    
    # 创建logger
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))
    
    # 避免重复添加handler
    if logger.handlers:
        return logger
    
    # 创建formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件handler
    log_file = Config.LOG_DIR / f"training_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    return logger


def log_system_info(logger: logging.Logger):
    """记录系统信息"""
    import torch
    import platform
    
    logger.info("="*50)
    logger.info("SYSTEM INFORMATION")
    logger.info("="*50)
    logger.info(f"Platform: {platform.platform()}")
    logger.info(f"Python version: {platform.python_version()}")
    logger.info(f"PyTorch version: {torch.__version__}")
    logger.info(f"CUDA available: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        logger.info(f"CUDA version: {torch.version.cuda}")
        logger.info(f"GPU count: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            logger.info(f"GPU {i}: {torch.cuda.get_device_name(i)}")
            logger.info(f"GPU {i} memory: {torch.cuda.get_device_properties(i).total_memory / 1024**3:.1f} GB")
    
    logger.info("="*50)


def log_config(logger: logging.Logger, config: Config):
    """记录配置信息"""
    logger.info("="*50)
    logger.info("CONFIGURATION")
    logger.info("="*50)
    
    config_attrs = [attr for attr in dir(config) if not attr.startswith('_') and not callable(getattr(config, attr))]
    
    for attr in sorted(config_attrs):
        value = getattr(config, attr)
        logger.info(f"{attr}: {value}")
    
    logger.info("="*50)


def log_model_info(logger: logging.Logger, model):
    """记录模型信息"""
    logger.info("="*50)
    logger.info("MODEL INFORMATION")
    logger.info("="*50)
    
    # 计算参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    logger.info(f"Model: {model.__class__.__name__}")
    logger.info(f"Total parameters: {total_params:,}")
    logger.info(f"Trainable parameters: {trainable_params:,}")
    logger.info(f"Non-trainable parameters: {total_params - trainable_params:,}")
    
    # 模型结构
    logger.info("Model structure:")
    for name, module in model.named_children():
        logger.info(f"  {name}: {module}")
    
    logger.info("="*50)


def log_dataset_info(logger: logging.Logger, train_loader, val_loader, test_loader):
    """记录数据集信息"""
    logger.info("="*50)
    logger.info("DATASET INFORMATION")
    logger.info("="*50)
    
    logger.info(f"Training samples: {len(train_loader.dataset)}")
    logger.info(f"Validation samples: {len(val_loader.dataset)}")
    logger.info(f"Test samples: {len(test_loader.dataset)}")
    logger.info(f"Batch size: {train_loader.batch_size}")
    logger.info(f"Number of classes: {len(train_loader.dataset.classes)}")
    logger.info(f"Classes: {train_loader.dataset.classes}")
    
    # 类别分布
    if hasattr(train_loader.dataset, 'samples'):
        class_counts = {}
        for _, label in train_loader.dataset.samples:
            class_name = train_loader.dataset.classes[label]
            class_counts[class_name] = class_counts.get(class_name, 0) + 1
        
        logger.info("Training set class distribution:")
        for class_name, count in class_counts.items():
            percentage = count / len(train_loader.dataset) * 100
            logger.info(f"  {class_name}: {count} ({percentage:.1f}%)")
    
    logger.info("="*50)
