import os
from pathlib import Path

class Config:
    """配置类，包含所有训练和模型参数"""

    # 数据路径配置
    DATA_ROOT = Path("data/medical")
    TRAIN_DIR = DATA_ROOT / "train"
    VAL_DIR = DATA_ROOT / "val"
    TEST_DIR = DATA_ROOT / "test"

    # 模型配置
    MODEL_NAME = "resnet50"
    NUM_CLASSES = None  # 将在运行时根据数据自动设置
    PRETRAINED = True
    PRETRAINED_PATH = "models/pretrained"  # 离线预训练模型路径

    # 训练配置
    BATCH_SIZE = 4
    NUM_EPOCHS = 100
    LEARNING_RATE = 0.001
    WEIGHT_DECAY = 1e-4
    MOMENTUM = 0.9

    # 数据预处理配置
    IMAGE_SIZE = (128, 128, 128)  # (D, H, W)
    SPACING = (1.0, 1.0, 1.0)  # 重采样间距
    INTENSITY_RANGE = (-1000, 1000)  # HU值范围

    # 数据增强配置
    USE_AUGMENTATION = True
    ROTATION_RANGE = 15  # 度
    TRANSLATION_RANGE = 0.1  # 相对于图像大小的比例
    SCALING_RANGE = 0.1

    # 训练设备配置
    DEVICE = "cuda" if os.environ.get("CUDA_VISIBLE_DEVICES") else "cpu"
    NUM_WORKERS = 4
    PIN_MEMORY = True

    # 输出配置
    OUTPUT_DIR = Path("outputs")
    MODEL_SAVE_DIR = OUTPUT_DIR / "models"
    LOG_DIR = OUTPUT_DIR / "logs"
    TENSORBOARD_DIR = OUTPUT_DIR / "tensorboard"

    # 验证和保存配置
    SAVE_EVERY_N_EPOCHS = 10
    VALIDATE_EVERY_N_EPOCHS = 1
    EARLY_STOPPING_PATIENCE = 20

    # 日志配置
    LOG_LEVEL = "INFO"
    SAVE_PREDICTIONS = True

    @classmethod
    def create_directories(cls):
        """创建必要的目录"""
        directories = [
            cls.OUTPUT_DIR,
            cls.MODEL_SAVE_DIR,
            cls.LOG_DIR,
            cls.TENSORBOARD_DIR,
            cls.PRETRAINED_PATH
        ]
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)

    @classmethod
    def update_num_classes(cls, num_classes):
        """更新类别数量"""
        cls.NUM_CLASSES = num_classes


