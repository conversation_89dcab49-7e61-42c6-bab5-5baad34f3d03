# 3D-ResNet50 医学影像分类

基于PyTorch的3D ResNet50医学影像分类项目，专门用于处理NII.gz格式的医学影像数据。

## 关键特性

- **3D ResNet50架构**: 专为3D医学影像设计的深度学习模型
- **自动类别发现**: 自动扫描训练目录中的类别，无需手动配置
- **NII.gz支持**: 原生支持医学影像标准格式
- **离线预训练**: 支持离线加载预训练模型权重
- **完整训练流程**: 包含训练、验证、测试和推理
- **数据增强**: 内置3D数据增强技术
- **可视化**: TensorBoard集成和训练过程可视化

## 技术栈

- **PyTorch**: 深度学习框架
- **SimpleITK**: 医学影像处理
- **3D ResNet50**: 核心模型架构
- **TensorBoard**: 训练监控
- **scikit-learn**: 评估指标

## 项目结构

```
3DTrain/
├── config/
│   └── config.py              # 配置文件
├── data/
│   └── medical/
│       ├── train/             # 训练数据
│       ├── val/               # 验证数据
│       └── test/              # 测试数据
├── src/
│   ├── dataset.py             # 数据加载和预处理
│   ├── model.py               # 3D ResNet模型实现
│   ├── trainer.py             # 训练和验证逻辑
│   ├── train.py               # 主训练脚本
│   └── inference.py           # 推理脚本
├── utils/
│   ├── logger.py              # 日志工具
│   ├── transforms.py          # 3D数据变换
│   └── data_preparation.py    # 数据准备工具
├── outputs/                   # 输出目录
│   ├── models/                # 保存的模型
│   ├── logs/                  # 日志文件
│   └── tensorboard/           # TensorBoard日志
├── requirements.txt           # 依赖包
└── README.md                  # 项目说明
```

## 快速开始

### 1. 环境安装

```bash
# 克隆项目
git clone <repository-url>
cd 3DTrain

# 安装依赖
pip install -r requirements.txt
```

### 2. 数据准备

将您的NII.gz文件按以下结构组织：

```
data/medical/train/
├── class1/
│   ├── image1.nii.gz
│   ├── image2.nii.gz
│   └── ...
├── class2/
│   ├── image1.nii.gz
│   ├── image2.nii.gz
│   └── ...
└── ...
```

**数据准备工具**:

```bash
# 扫描数据目录
python utils/data_preparation.py --scan data/medical/train

# 自动划分数据集（如果只有一个目录）
python utils/data_preparation.py --split data/medical/all_data

# 验证数据集结构
python utils/data_preparation.py --validate data/medical
```

### 3. 训练模型

```bash
# 基本训练
python src/train.py

# 自定义参数训练
python src/train.py \
    --model-name resnet50 \
    --batch-size 4 \
    --epochs 100 \
    --lr 0.001 \
    --device cuda

# 使用预训练模型
python src/train.py --pretrained-path models/pretrained/resnet50_3d.pth
```

### 4. 模型推理

```bash
# 单个文件推理
python src/inference.py \
    --model-path outputs/models/best_model.pth \
    --image-path path/to/your/image.nii.gz \
    --num-classes 2
```

## 配置说明

主要配置在 `config/config.py` 中：

```python
# 数据配置
IMAGE_SIZE = (128, 128, 128)      # 图像尺寸 (D, H, W)
SPACING = (1.0, 1.0, 1.0)         # 重采样间距
INTENSITY_RANGE = (-1000, 1000)   # HU值范围

# 训练配置
BATCH_SIZE = 4                    # 批次大小
NUM_EPOCHS = 100                  # 训练轮数
LEARNING_RATE = 0.001             # 学习率

# 数据增强
USE_AUGMENTATION = True           # 是否使用数据增强
ROTATION_RANGE = 15               # 旋转角度范围
```

## 模型架构

项目实现了完整的3D ResNet架构：

- **ResNet18-3D**: 轻量级版本，适合小数据集
- **ResNet34-3D**: 中等复杂度
- **ResNet50-3D**: 标准版本，推荐使用

支持的功能：
- 离线预训练模型加载
- 自定义类别数量
- 灵活的输入通道数

## 数据增强

内置3D数据增强技术：

- **随机旋转**: 3D空间随机旋转
- **随机平移**: 空间位置随机偏移
- **随机缩放**: 尺寸随机变化
- **随机翻转**: 轴向随机翻转
- **随机噪声**: 添加高斯噪声

## 训练监控

- **TensorBoard**: 实时监控训练过程
- **日志记录**: 详细的训练日志
- **模型保存**: 自动保存最佳模型
- **早停机制**: 防止过拟合

启动TensorBoard：
```bash
tensorboard --logdir outputs/tensorboard
```

## 评估指标

- **准确率**: 分类准确率
- **精确率**: Precision
- **召回率**: Recall
- **F1分数**: F1-Score
- **混淆矩阵**: 详细分类结果

## 常见问题

### Q: 如何处理内存不足？
A: 减小 `BATCH_SIZE` 或 `IMAGE_SIZE`，或使用梯度累积。

### Q: 训练速度慢怎么办？
A: 确保使用GPU，增加 `NUM_WORKERS`，或使用混合精度训练。

### Q: 如何使用自己的预训练模型？
A: 将模型权重放在 `models/pretrained/` 目录，并在训练时指定路径。

### Q: 支持哪些图像格式？
A: 主要支持 `.nii.gz` 和 `.nii` 格式，这是医学影像的标准格式。

## 许可证

[MIT License](LICENSE)

## 贡献

欢迎提交Issue和Pull Request！

## 联系方式

如有问题，请通过Issue联系。